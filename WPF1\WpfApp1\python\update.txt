=== FULL DEBUG MODE - ALL MESSAGES ===
Total Messages: 550
Last Updated: 23:57:09.514
Update Frequency: Every 1000ms (1 second)

  [2025-08-01 23:53:33.508] [System] Debug cleared and reinitialized
  [2025-08-01 23:53:33.509] [System] Debug file cleared and reset: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\debug\full_debug.txt
  [2025-08-01 23:53:33.509] [System] File ready for new debug messages in Full debug mode
  [2025-08-01 23:53:33.509] [System] Debug display and file manually cleared at 23:53:33.509
  [2025-08-01 23:53:33.509] [PropertyChanged] StatusMessage changed
  [2025-08-01 23:53:33.509] [FullDebug-PropertyChange] StatusMessage = 'Debug display and file cleared at 23:53:33'
  [2025-08-01 23:53:34.492] [PropertyChanged] SelectedTabIndex changed
  [2025-08-01 23:53:34.492] [FullDebug-PropertyChange] SelectedTabIndex = Tab 3 (Settings)
  [2025-08-01 23:53:34.492] [Navigation] Tab switched to: Settings (index 3)
  [2025-08-01 23:53:34.492] [FullDebug-UI] Tab Switch -> Clicked (Switched to Settings tab (index 3))
  [2025-08-01 23:53:34.492] [FullDebug-Navigation] User navigated from tab 4 to tab 3
  [2025-08-01 23:53:34.495] [Settings] 🔄 ConnectionSettingsView loaded, refreshing network adapter binding
  [2025-08-01 23:53:34.495] [Settings] 🔄 RefreshNetworkAdapterProperties called
  [2025-08-01 23:53:34.495] [Settings]    Current SelectedNetworkAdapter: 'auto'
  [2025-08-01 23:53:34.495] [PropertyChanged] Settings.AvailableNetworkAdapters changed
  [2025-08-01 23:53:34.495] [FullDebug-PropertyChange] Settings.AvailableNetworkAdapters = Value not tracked
  [2025-08-01 23:53:34.495] [Settings] ✅ Network adapter properties refresh initiated on ConnectionSettingsView load
  [2025-08-01 23:53:34.595] [PropertyChanged] Settings.SelectedNetworkAdapter changed
  [2025-08-01 23:53:34.595] [FullDebug-PropertyChange] Settings.SelectedNetworkAdapter = Value not tracked
  [2025-08-01 23:53:34.595] [Settings] ✅ RefreshNetworkAdapterProperties completed
  [2025-08-01 23:53:34.606] [PropertyChanged] Settings.AvailableNetworkAdapters changed
  [2025-08-01 23:53:34.606] [FullDebug-PropertyChange] Settings.AvailableNetworkAdapters = Value not tracked
  [2025-08-01 23:53:34.606] [PropertyChanged] Settings.SelectedNetworkAdapter changed
  [2025-08-01 23:53:34.606] [FullDebug-PropertyChange] Settings.SelectedNetworkAdapter = Value not tracked
  [2025-08-01 23:53:34.606] [Settings] 🔄 Network adapter UI binding refreshed via Dispatcher
  [2025-08-01 23:53:38.056] [Settings] 🔄 ConnectionSettingsView loaded, refreshing network adapter binding
  [2025-08-01 23:53:38.056] [Settings] 🔄 RefreshNetworkAdapterProperties called
  [2025-08-01 23:53:38.056] [Settings]    Current SelectedNetworkAdapter: 'auto'
  [2025-08-01 23:53:38.056] [PropertyChanged] Settings.AvailableNetworkAdapters changed
  [2025-08-01 23:53:38.056] [FullDebug-PropertyChange] Settings.AvailableNetworkAdapters = Value not tracked
  [2025-08-01 23:53:38.056] [Settings] ✅ Network adapter properties refresh initiated on ConnectionSettingsView load
  [2025-08-01 23:53:38.157] [PropertyChanged] Settings.SelectedNetworkAdapter changed
  [2025-08-01 23:53:38.157] [FullDebug-PropertyChange] Settings.SelectedNetworkAdapter = Value not tracked
  [2025-08-01 23:53:38.157] [Settings] ✅ RefreshNetworkAdapterProperties completed
  [2025-08-01 23:53:38.157] [PropertyChanged] Settings.AvailableNetworkAdapters changed
  [2025-08-01 23:53:38.157] [FullDebug-PropertyChange] Settings.AvailableNetworkAdapters = Value not tracked
  [2025-08-01 23:53:38.157] [PropertyChanged] Settings.SelectedNetworkAdapter changed
  [2025-08-01 23:53:38.157] [FullDebug-PropertyChange] Settings.SelectedNetworkAdapter = Value not tracked
  [2025-08-01 23:53:38.157] [Settings] 🔄 Network adapter UI binding refreshed via Dispatcher
  [2025-08-01 23:53:40.779] [PropertyChanged] SelectedTabIndex changed
  [2025-08-01 23:53:40.780] [FullDebug-PropertyChange] SelectedTabIndex = Tab 0 (Fetching Settings)
  [2025-08-01 23:53:40.780] [Navigation] Tab switched to: Fetching Settings (index 0)
  [2025-08-01 23:53:40.780] [FullDebug-UI] Tab Switch -> Clicked (Switched to Fetching Settings tab (index 0))
  [2025-08-01 23:53:40.780] [FullDebug-Navigation] User navigated from tab 3 to tab 0
  [2025-08-01 23:53:40.876] [NetworkAdapter] Found 9 total network interfaces
  [2025-08-01 23:53:40.876] [NetworkAdapter]   - Ethernet 2: Ethernet, Status: Down
  [2025-08-01 23:53:40.876] [NetworkAdapter]   - Local Area Connection: 53, Status: Down
  [2025-08-01 23:53:40.876] [NetworkAdapter]   - PdaNet Broadband Connection: Ethernet, Status: Down
  [2025-08-01 23:53:40.876] [NetworkAdapter]   - Local Area Connection* 1: Wireless80211, Status: Down [WiFi]
  [2025-08-01 23:53:40.876] [NetworkAdapter]   - Local Area Connection* 2: Wireless80211, Status: Down [WiFi]
  [2025-08-01 23:53:40.876] [NetworkAdapter]   - Ethernet: Ethernet, Status: Up
  [2025-08-01 23:53:40.876] [NetworkAdapter]   - Wi-Fi: Wireless80211, Status: Down [WiFi]
  [2025-08-01 23:53:40.876] [NetworkAdapter]   - vEthernet (Default Switch): Ethernet, Status: Up
  [2025-08-01 23:53:40.876] [NetworkAdapter]   - Loopback Pseudo-Interface 1: Loopback, Status: Up
  [2025-08-01 23:53:40.876] [NetworkAdapter] Filtering out virtual WiFi adapter: Local Area Connection* 1 (Microsoft Wi-Fi Direct Virtual Adapter)
  [2025-08-01 23:53:40.876] [NetworkAdapter] Filtering out virtual WiFi adapter: Local Area Connection* 2 (Microsoft Wi-Fi Direct Virtual Adapter #2)
  [2025-08-01 23:53:40.876] [NetworkAdapter] Filtering out virtual WiFi adapter: Local Area Connection* 1 (Microsoft Wi-Fi Direct Virtual Adapter)
  [2025-08-01 23:53:40.876] [NetworkAdapter] Filtering out virtual WiFi adapter: Local Area Connection* 2 (Microsoft Wi-Fi Direct Virtual Adapter #2)
  [2025-08-01 23:53:40.876] [NetworkAdapter] After filtering: 3 suitable network interfaces (1 WiFi adapters)
  [2025-08-01 23:53:40.876] [NetworkAdapter] Filtering out virtual WiFi adapter: Local Area Connection* 1 (Microsoft Wi-Fi Direct Virtual Adapter)
  [2025-08-01 23:53:40.876] [NetworkAdapter] Filtering out virtual WiFi adapter: Local Area Connection* 2 (Microsoft Wi-Fi Direct Virtual Adapter #2)
  [2025-08-01 23:53:40.876] [NetworkAdapter] Added adapter: Ethernet (Ethernet) - Status: Up
  [2025-08-01 23:53:40.876] [NetworkAdapter] Added adapter: vEthernet (Default Switch) (Ethernet) - Status: Up
  [2025-08-01 23:53:40.876] [NetworkAdapter] Added adapter: Wi-Fi (Wi-Fi) - Status: Down
  [2025-08-01 23:53:40.876] [NetworkAdapter] Found 3 network adapters (plus Auto option)
  [2025-08-01 23:53:40.876] [CustomRules] NetworkAdapterService returned 4 adapters
  [2025-08-01 23:53:40.876] [CustomRules]   - Auto (System Default) (auto) - Auto
  [2025-08-01 23:53:40.876] [CustomRules]   - Ethernet ({6EECE4EF-61E3-434D-AE99-23853E80916F}) - Ethernet
  [2025-08-01 23:53:40.876] [CustomRules]   - vEthernet (Default Switch) ({0F4DAF6C-E4AB-40E4-A17D-C9ACC093EF53}) - Ethernet
  [2025-08-01 23:53:40.876] [CustomRules]   - Wi-Fi ({3F1854FC-3D4E-4695-A19F-02A562B3FA98}) - Wi-Fi
  [2025-08-01 23:53:40.878] [PropertyChanged] AvailableNetworkAdapters changed
  [2025-08-01 23:53:40.878] [FullDebug-PropertyChange] AvailableNetworkAdapters = Value not tracked
  [2025-08-01 23:53:40.878] [CustomRules] Refreshed custom rules with 4 network adapters
  [2025-08-01 23:53:40.878] [CustomRules] Loaded 0 rules from disk
  [2025-08-01 23:53:40.878] [CustomRules] Loaded 0 saved rules
  [2025-08-01 23:53:42.492] [PropertyChanged] SelectedTabIndex changed
  [2025-08-01 23:53:42.492] [FullDebug-PropertyChange] SelectedTabIndex = Tab 1 (Testing)
  [2025-08-01 23:53:42.492] [Navigation] Tab switched to: Testing (index 1)
  [2025-08-01 23:53:42.492] [FullDebug-UI] Tab Switch -> Clicked (Switched to Testing tab (index 1))
  [2025-08-01 23:53:42.492] [FullDebug-Navigation] User navigated from tab 0 to tab 1
  [2025-08-01 23:53:44.667] [Connection] Found sing-box.exe in application base directory: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\bin\Debug\net8.0-windows\sing-box.exe
  [2025-08-01 23:53:44.667] [PropertyChanged] StatusMessage changed
  [2025-08-01 23:53:44.667] [FullDebug-PropertyChange] StatusMessage = 'Connecting to proxy (TUN mode)...'
  [2025-08-01 23:53:44.667] [Action] ConnectProxyTUN - Attempting TUN connection to 🅼🅴🅷🅳🅸 (brainy-party.seotoolsforyou.co.uk:80)
  [2025-08-01 23:53:44.667] [FullDebug-Action] ConnectProxyTUN - Attempting TUN connection to 🅼🅴🅷🅳🅸 (brainy-party.seotoolsforyou.co.uk:80)
  [2025-08-01 23:53:44.667] [User] ConnectProxyTUN (User initiated TUN connection to 🅼🅴🅷🅳🅸)
  [2025-08-01 23:53:44.667] [FullDebug-UserInteraction] ConnectProxyTUN (User initiated TUN connection to 🅼🅴🅷🅳🅸)
  [2025-08-01 23:53:44.667] [FullDebug-Connection] Starting TUN connection process for 🅼🅴🅷🅳🅸 - Protocol: vless
  [2025-08-01 23:53:44.672] [Connection] 🧹 Starting comprehensive cleanup before new connection
  [2025-08-01 23:53:44.706] [Connection] 🔄 Resetting system proxy settings
  [2025-08-01 23:53:44.723] [Connection] 🔄 Flushing DNS cache to prevent resolution issues
  [2025-08-01 23:53:44.779] [Connection] ✅ DNS cache flushed successfully
  [2025-08-01 23:53:45.781] [Connection] ✅ Comprehensive cleanup completed successfully
  [2025-08-01 23:53:45.782] [Connection] Found xray.exe in application base directory: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\bin\Debug\net8.0-windows\xray.exe
  [2025-08-01 23:53:45.782] [Connection] Found xray.exe in application base directory: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\bin\Debug\net8.0-windows\xray.exe
  [2025-08-01 23:53:45.786] [Connection] 🔄 Clearing DNS cache before TUN connection
  [2025-08-01 23:53:45.786] [Connection] 🔄 Flushing DNS cache to prevent resolution issues
  [2025-08-01 23:53:45.819] [Connection] ✅ DNS cache flushed successfully
  [2025-08-01 23:53:45.824] [Connection] Starting elevated helper process
  [2025-08-01 23:53:45.827] [ElevatedTunService] Elevated helper stopped
  [2025-08-01 23:53:45.828] [ElevatedTunService] Starting elevated helper: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\bin\Debug\net8.0-windows\ElevatedTunHelper.exe
  [2025-08-01 23:53:45.830] [Settings] 🔧 MaxConcurrentTests setter called: 10 -> 37
  [2025-08-01 23:53:45.830] [Settings] 🔧 MaxConcurrentTests clamped value: 37
  [2025-08-01 23:53:45.830] [Settings] ✅ MaxConcurrentTests property changed, triggering OnTestSettingsChanged()
  [2025-08-01 23:53:45.830] [Settings] 🔔 OnTestSettingsChanged() called
  [2025-08-01 23:53:45.830] [Settings] 📊 Current values: MaxConcurrent=37, Timeout=10000ms
  [2025-08-01 23:53:45.830] [Settings] 📡 TestSettingsChanged event has 0 subscribers
  [2025-08-01 23:53:45.830] [Settings] ⚠️ TestSettingsChanged event is null - no subscribers!
  [2025-08-01 23:53:45.830] [Settings] 🔧 TestTimeoutSeconds setter called: 10s -> 10s
  [2025-08-01 23:53:45.830] [Settings] 🔧 TestTimeoutSeconds converted to milliseconds: 10000ms
  [2025-08-01 23:53:45.830] [Settings] ⏭️ TestTimeoutSeconds property unchanged
  [2025-08-01 23:53:45.830] [Settings] 🔔 OnConnectionSettingsChanged() called
  [2025-08-01 23:53:45.830] [Settings] 📊 Current connection values: SelectedNetworkAdapter=auto, UseGeolocationRouting=True, PreventMitmAttacks=True, UseDnsOverHttps=True, BlockQuicProtocol=True
  [2025-08-01 23:53:45.830] [Settings] 📡 ConnectionSettingsChanged event has 0 subscribers
  [2025-08-01 23:53:45.830] [Settings] ⚠️ ConnectionSettingsChanged event is null - no subscribers!
  [2025-08-01 23:53:45.830] [Settings] 🔔 OnConnectionSettingsChanged() called
  [2025-08-01 23:53:45.830] [Settings] 📊 Current connection values: SelectedNetworkAdapter=auto, UseGeolocationRouting=True, PreventMitmAttacks=True, UseDnsOverHttps=False, BlockQuicProtocol=True
  [2025-08-01 23:53:45.830] [Settings] 📡 ConnectionSettingsChanged event has 0 subscribers
  [2025-08-01 23:53:45.830] [Settings] ⚠️ ConnectionSettingsChanged event is null - no subscribers!
  [2025-08-01 23:53:45.830] [Settings] 🔧 MaxConcurrentDownloadTests setter called: 3 -> 3
  [2025-08-01 23:53:45.830] [Settings] 🔧 MaxConcurrentDownloadTests clamped value: 3
  [2025-08-01 23:53:45.830] [Settings] ⏭️ MaxConcurrentDownloadTests property unchanged
  [2025-08-01 23:53:45.830] [Settings] 🔧 DownloadTestTimeoutSeconds setter called: 30 -> 30
  [2025-08-01 23:53:45.830] [Settings] 🔧 DownloadTestTimeoutSeconds clamped value: 30
  [2025-08-01 23:53:45.830] [Settings] ⏭️ DownloadTestTimeoutSeconds property unchanged
  [2025-08-01 23:53:45.830] [Settings] 🔧 DownloadTestSource setter called: http://ipv4.download.thinkbroadband.com/1MB.zip -> http://ipv4.download.thinkbroadband.com/1MB.zip
  [2025-08-01 23:53:45.830] [Settings] ⏭️ DownloadTestSource property unchanged
  [2025-08-01 23:53:45.830] [Settings] 🔧 DownloadTestMethod setter called: StaticDownload -> StaticDownload
  [2025-08-01 23:53:45.830] [Settings] ⏭️ DownloadTestMethod property unchanged
  [2025-08-01 23:53:47.108] [ElevatedTunService] Elevated helper started with PID: 20404
  [2025-08-01 23:53:50.109] [ElevatedTunService] Attempting IPC connection to pipe: ElevatedTunHelper_alire
  [2025-08-01 23:53:50.127] [ElevatedTunService] Starting IPC connection attempt...
  [2025-08-01 23:53:50.131] [IpcClient] Creating pipe client for: ElevatedTunHelper_alire
  [2025-08-01 23:53:50.131] [IpcClient] Attempting to connect to pipe with 15000ms timeout
  [2025-08-01 23:53:50.133] [IpcClient] Pipe connection established, waiting for server handshake
  [2025-08-01 23:53:50.149] [IpcClient] Received READY from server, sending ACK
  [2025-08-01 23:53:50.150] [IpcClient] Sent ACK to server, starting raw pipe communication
  [2025-08-01 23:53:50.153] [IpcClient] IPC client connection fully established
  [2025-08-01 23:53:50.153] [ElevatedTunService] IPC connection established successfully
  [2025-08-01 23:53:50.208] [IpcClient] Sent IPC message: Heartbeat
  [2025-08-01 23:53:50.262] [IpcClient] Received IPC response: {"RequestId":"aef91361-9137-4181-a278-4b83584b5177","Type":7,"Timestamp":"2025-08-01T20:23:50.2578912Z","Data":null,"Error":null}
  [2025-08-01 23:53:50.267] [ElevatedTunService] Successfully connected to elevated helper
  [2025-08-01 23:53:50.267] [Connection] Elevated helper started successfully
  [2025-08-01 23:53:50.334] [Connection] SingBox config exported to: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\debug\singbox_config.txt
  [2025-08-01 23:53:50.334] [Connection] Starting TUN connection via elevated helper
  [2025-08-01 23:53:50.335] [ElevatedTunService] Starting TUN connection for proxy: 🅼🅴🅷🅳🅸
  [2025-08-01 23:53:50.336] [Settings] 🔧 MaxConcurrentTests setter called: 10 -> 37
  [2025-08-01 23:53:50.336] [Settings] 🔧 MaxConcurrentTests clamped value: 37
  [2025-08-01 23:53:50.336] [Settings] ✅ MaxConcurrentTests property changed, triggering OnTestSettingsChanged()
  [2025-08-01 23:53:50.336] [Settings] 🔔 OnTestSettingsChanged() called
  [2025-08-01 23:53:50.336] [Settings] 📊 Current values: MaxConcurrent=37, Timeout=10000ms
  [2025-08-01 23:53:50.336] [Settings] 📡 TestSettingsChanged event has 0 subscribers
  [2025-08-01 23:53:50.336] [Settings] ⚠️ TestSettingsChanged event is null - no subscribers!
  [2025-08-01 23:53:50.336] [Settings] 🔧 TestTimeoutSeconds setter called: 10s -> 10s
  [2025-08-01 23:53:50.336] [Settings] 🔧 TestTimeoutSeconds converted to milliseconds: 10000ms
  [2025-08-01 23:53:50.336] [Settings] ⏭️ TestTimeoutSeconds property unchanged
  [2025-08-01 23:53:50.336] [Settings] 🔔 OnConnectionSettingsChanged() called
  [2025-08-01 23:53:50.336] [Settings] 📊 Current connection values: SelectedNetworkAdapter=auto, UseGeolocationRouting=True, PreventMitmAttacks=True, UseDnsOverHttps=True, BlockQuicProtocol=True
  [2025-08-01 23:53:50.336] [Settings] 📡 ConnectionSettingsChanged event has 0 subscribers
  [2025-08-01 23:53:50.336] [Settings] ⚠️ ConnectionSettingsChanged event is null - no subscribers!
  [2025-08-01 23:53:50.336] [Settings] 🔔 OnConnectionSettingsChanged() called
  [2025-08-01 23:53:50.336] [Settings] 📊 Current connection values: SelectedNetworkAdapter=auto, UseGeolocationRouting=True, PreventMitmAttacks=True, UseDnsOverHttps=False, BlockQuicProtocol=True
  [2025-08-01 23:53:50.336] [Settings] 📡 ConnectionSettingsChanged event has 0 subscribers
  [2025-08-01 23:53:50.336] [Settings] ⚠️ ConnectionSettingsChanged event is null - no subscribers!
  [2025-08-01 23:53:50.336] [Settings] 🔧 MaxConcurrentDownloadTests setter called: 3 -> 3
  [2025-08-01 23:53:50.336] [Settings] 🔧 MaxConcurrentDownloadTests clamped value: 3
  [2025-08-01 23:53:50.336] [Settings] ⏭️ MaxConcurrentDownloadTests property unchanged
  [2025-08-01 23:53:50.336] [Settings] 🔧 DownloadTestTimeoutSeconds setter called: 30 -> 30
  [2025-08-01 23:53:50.336] [Settings] 🔧 DownloadTestTimeoutSeconds clamped value: 30
  [2025-08-01 23:53:50.336] [Settings] ⏭️ DownloadTestTimeoutSeconds property unchanged
  [2025-08-01 23:53:50.336] [Settings] 🔧 DownloadTestSource setter called: http://ipv4.download.thinkbroadband.com/1MB.zip -> http://ipv4.download.thinkbroadband.com/1MB.zip
  [2025-08-01 23:53:50.336] [Settings] ⏭️ DownloadTestSource property unchanged
  [2025-08-01 23:53:50.336] [Settings] 🔧 DownloadTestMethod setter called: StaticDownload -> StaticDownload
  [2025-08-01 23:53:50.336] [Settings] ⏭️ DownloadTestMethod property unchanged
  [2025-08-01 23:53:50.336] [ElevatedTunService] Debug logs export: False
  [2025-08-01 23:53:50.336] [ElevatedTunService] TUN start attempt 1/3
  [2025-08-01 23:53:50.351] [IpcClient] Sent IPC message: StartTun
  [2025-08-01 23:53:58.715] [IpcClient] Received IPC response: {"RequestId":"59f9d5ab-465f-44e8-a4ef-f80ca261e292","Type":5,"Timestamp":"2025-08-01T20:23:58.711207Z","Data":"TUN started successfully","Error":null}
  [2025-08-01 23:53:58.718] [Connection] TUN connection started successfully via elevated helper
  [2025-08-01 23:53:58.718] [IpcClient] TUN started successfully via elevated helper
  [2025-08-01 23:53:58.718] [ElevatedTunService] TUN started successfully
  [2025-08-01 23:53:59.721] [IpcClient] Sent IPC message: StatusRequest
  [2025-08-01 23:53:59.743] [IpcClient] Received IPC response: {"Status":2,"ProcessId":27808,"StartTime":"2025-08-01T23:53:50.6287902+03:30","CurrentProxy":"\uD83C\uDD7C\uD83C\uDD74\uD83C\uDD77\uD83C\uDD73\uD83C\uDD78","LocalPort":31298,"LastError":null,"RequestId":"cfefe558-8561-4393-ab44-a072d1ebe683","Type":3,"Timestamp":"2025-08-01T20:23:59.7222883Z","Data":null,"Error":null}
  [2025-08-01 23:53:59.762] [Connection] TUN process verified as Running after 1000ms
  [2025-08-01 23:53:59.762] [Connection] 🔍 Verifying TUN interface functionality
  [2025-08-01 23:53:59.884] [Connection] ✅ TUN interface found: singbox_tun (Status: Up)
  [2025-08-01 23:53:59.884] [Connection] 🔍 Testing DNS resolution through TUN interface
  [2025-08-01 23:53:59.886] [Connection] 🔍 Testing DNS resolution with 15s timeout (DoH: True, Exposed: False)
  [2025-08-01 23:54:00.004] [Connection] ✅ DNS resolution successful with 1 addresses
  [2025-08-01 23:54:00.004] [Connection] 🔍 Testing basic connectivity through proxy
  [2025-08-01 23:54:00.103] [Connection] ❌ Proxy connectivity test failed
  [2025-08-01 23:54:00.104] [Connection] TUN connection failed: TUN interface verification failed - interface not functional
  [2025-08-01 23:54:00.114] [IpcClient] Sent IPC message: StopTun
  [2025-08-01 23:54:06.139] [IpcClient] Received IPC response: {"RequestId":"1dd71ce7-0328-40eb-a252-6a8866e5731c","Type":5,"Timestamp":"2025-08-01T20:24:06.1393095Z","Data":"TUN stopped successfully","Error":null}
  [2025-08-01 23:54:06.140] [IpcClient] TUN stopped successfully via elevated helper
  [2025-08-01 23:54:06.142] [IpcClient] Sent IPC message: StopTun
  [2025-08-01 23:54:06.143] [IpcClient] Received IPC response: {"RequestId":"7fe9dd1b-0689-4b6e-88bd-971f2147ffda","Type":5,"Timestamp":"2025-08-01T20:24:06.143383Z","Data":"TUN stopped successfully","Error":null}
  [2025-08-01 23:54:06.143] [IpcClient] TUN stopped successfully via elevated helper
  [2025-08-01 23:54:06.144] [Connection] Found xray.exe in application base directory: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\bin\Debug\net8.0-windows\xray.exe
  [2025-08-01 23:54:06.144] [Connection] Found xray.exe in application base directory: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\bin\Debug\net8.0-windows\xray.exe
  [2025-08-01 23:54:06.144] [Error] ConnectProxyTUN failed: Connection failed: TUN interface verification failed - interface not functional
  [2025-08-01 23:54:06.144] [Error] Inner exception: TUN interface verification failed - interface not functional
  [2025-08-01 23:54:06.144] [PropertyChanged] StatusMessage changed
  [2025-08-01 23:54:06.144] [FullDebug-PropertyChange] StatusMessage = 'TUN connection failed: Connection failed: TUN interface verification failed - interface not functional'
  [2025-08-01 23:54:06.999] [PropertyChanged] SelectedTabIndex changed
  [2025-08-01 23:54:06.999] [FullDebug-PropertyChange] SelectedTabIndex = Tab 3 (Settings)
  [2025-08-01 23:54:06.999] [Navigation] Tab switched to: Settings (index 3)
  [2025-08-01 23:54:06.999] [FullDebug-UI] Tab Switch -> Clicked (Switched to Settings tab (index 3))
  [2025-08-01 23:54:06.999] [FullDebug-Navigation] User navigated from tab 1 to tab 3
  [2025-08-01 23:54:07.002] [Settings] 🔄 ConnectionSettingsView loaded, refreshing network adapter binding
  [2025-08-01 23:54:07.002] [Settings] 🔄 RefreshNetworkAdapterProperties called
  [2025-08-01 23:54:07.002] [Settings]    Current SelectedNetworkAdapter: 'auto'
  [2025-08-01 23:54:07.002] [PropertyChanged] Settings.AvailableNetworkAdapters changed
  [2025-08-01 23:54:07.002] [FullDebug-PropertyChange] Settings.AvailableNetworkAdapters = Value not tracked
  [2025-08-01 23:54:07.002] [Settings] ✅ Network adapter properties refresh initiated on ConnectionSettingsView load
  [2025-08-01 23:54:07.102] [PropertyChanged] Settings.SelectedNetworkAdapter changed
  [2025-08-01 23:54:07.102] [FullDebug-PropertyChange] Settings.SelectedNetworkAdapter = Value not tracked
  [2025-08-01 23:54:07.102] [Settings] ✅ RefreshNetworkAdapterProperties completed
  [2025-08-01 23:54:07.108] [PropertyChanged] Settings.AvailableNetworkAdapters changed
  [2025-08-01 23:54:07.108] [FullDebug-PropertyChange] Settings.AvailableNetworkAdapters = Value not tracked
  [2025-08-01 23:54:07.108] [PropertyChanged] Settings.SelectedNetworkAdapter changed
  [2025-08-01 23:54:07.108] [FullDebug-PropertyChange] Settings.SelectedNetworkAdapter = Value not tracked
  [2025-08-01 23:54:07.108] [Settings] 🔄 Network adapter UI binding refreshed via Dispatcher
  [2025-08-01 23:54:08.370] [Settings] 🔔 OnConnectionSettingsChanged() called
  [2025-08-01 23:54:08.370] [Settings] 📊 Current connection values: SelectedNetworkAdapter=auto, UseGeolocationRouting=True, PreventMitmAttacks=True, UseDnsOverHttps=False, BlockQuicProtocol=True
  [2025-08-01 23:54:08.370] [Settings] 📡 ConnectionSettingsChanged event has 1 subscribers
  [2025-08-01 23:54:08.370] [Settings] 🚀 Invoking ConnectionSettingsChanged event...
  [2025-08-01 23:54:08.370] [Settings] ✅ ConnectionSettingsChanged event invoked successfully
  [2025-08-01 23:54:08.371] [Settings] 🔔 *** PROPERTY CHANGED: UseExposedDns (POST-LOAD) ***
  [2025-08-01 23:54:08.371] [Settings] 💾 Auto-saving settings after property change: UseExposedDns
  [2025-08-01 23:54:08.372] [Settings] 🔔 *** CONNECTION SETTINGS CHANGED EVENT TRIGGERED (POST-LOAD) ***
  [2025-08-01 23:54:08.372] [Settings]    SelectedNetworkAdapter: auto
  [2025-08-01 23:54:08.372] [Settings]    UseGeolocationRouting: True
  [2025-08-01 23:54:08.372] [Settings]    PreventMitmAttacks: True
  [2025-08-01 23:54:08.372] [Settings]    UseDnsOverHttps: False
  [2025-08-01 23:54:08.372] [Settings]    BlockQuicProtocol: True
  [2025-08-01 23:54:08.372] [Settings] Applying connection settings: UseGeolocationRouting=True, PreventMitmAttacks=True, UseDnsOverHttps=False, BlockQuicProtocol=True
  [2025-08-01 23:54:08.372] [Connection] Geolocation routing setting updated: True
  [2025-08-01 23:54:08.372] [Connection] Security settings updated - MITM Prevention: True, DNS over HTTPS: False, Block QUIC: True, Exposed DNS: True
  [2025-08-01 23:54:08.372] [Settings] Applied connection settings to service - Geolocation: True, MITM Prevention: True, DNS over HTTPS: False, Block QUIC: True, Exposed DNS: True
  [2025-08-01 23:54:08.372] [Settings] 💾 Auto-saving settings after connection settings change
  [2025-08-01 23:54:08.372] [Settings] ✅ Connection settings changed - auto-saved successfully
  [2025-08-01 23:54:08.374] [Storage] 💾 Saving settings to: C:\Users\<USER>\UltimateV2ray\settings.json
  [2025-08-01 23:54:08.374] [Storage] 💾 Saving settings to: C:\Users\<USER>\UltimateV2ray\settings.json
  [2025-08-01 23:54:08.374] [Storage] 📊 Settings summary: MaxConcurrent=37, Timeout=10000ms, URL=http://cp.cloudflare.com/
  [2025-08-01 23:54:08.374] [Storage] 📊 Settings summary: MaxConcurrent=37, Timeout=10000ms, URL=http://cp.cloudflare.com/
  [2025-08-01 23:54:08.379] [Storage] 📄 Settings JSON size: 1165 characters
  [2025-08-01 23:54:08.379] [Storage] 📄 Settings JSON size: 1165 characters
  [2025-08-01 23:54:08.379] [Storage] ❌ Error saving settings: The process cannot access the file 'C:\Users\<USER>\UltimateV2ray\settings.json' because it is being used by another process.
  [2025-08-01 23:54:08.379] [Error] Inner exception: The process cannot access the file 'C:\Users\<USER>\UltimateV2ray\settings.json' because it is being used by another process.
  [2025-08-01 23:54:08.379] [Error] Settings failed: Failed to save settings: The process cannot access the file 'C:\Users\<USER>\UltimateV2ray\settings.json' because it is being used by another process.
  [2025-08-01 23:54:08.380] [Storage] ✅ Settings saved successfully - File size: 1165 bytes
  [2025-08-01 23:54:08.380] [Settings] ✅ Settings auto-saved successfully after connection settings change
  [2025-08-01 23:54:10.877] [PropertyChanged] SelectedTabIndex changed
  [2025-08-01 23:54:10.877] [FullDebug-PropertyChange] SelectedTabIndex = Tab 1 (Testing)
  [2025-08-01 23:54:10.877] [Navigation] Tab switched to: Testing (index 1)
  [2025-08-01 23:54:10.877] [FullDebug-UI] Tab Switch -> Clicked (Switched to Testing tab (index 1))
  [2025-08-01 23:54:10.877] [FullDebug-Navigation] User navigated from tab 3 to tab 1
  [2025-08-01 23:54:12.777] [Connection] Found sing-box.exe in application base directory: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\bin\Debug\net8.0-windows\sing-box.exe
  [2025-08-01 23:54:12.777] [PropertyChanged] StatusMessage changed
  [2025-08-01 23:54:12.777] [FullDebug-PropertyChange] StatusMessage = 'Connecting to proxy (TUN mode)...'
  [2025-08-01 23:54:12.777] [Action] ConnectProxyTUN - Attempting TUN connection to 🅼🅴🅷🅳🅸 (brainy-party.seotoolsforyou.co.uk:80)
  [2025-08-01 23:54:12.777] [FullDebug-Action] ConnectProxyTUN - Attempting TUN connection to 🅼🅴🅷🅳🅸 (brainy-party.seotoolsforyou.co.uk:80)
  [2025-08-01 23:54:12.777] [User] ConnectProxyTUN (User initiated TUN connection to 🅼🅴🅷🅳🅸)
  [2025-08-01 23:54:12.777] [FullDebug-UserInteraction] ConnectProxyTUN (User initiated TUN connection to 🅼🅴🅷🅳🅸)
  [2025-08-01 23:54:12.777] [FullDebug-Connection] Starting TUN connection process for 🅼🅴🅷🅳🅸 - Protocol: vless
  [2025-08-01 23:54:12.777] [Connection] 🧹 Starting comprehensive cleanup before new connection
  [2025-08-01 23:54:12.787] [Connection] 🛑 Stopping elevated TUN service
  [2025-08-01 23:54:12.793] [IpcClient] Sent IPC message: StopTun
  [2025-08-01 23:54:12.798] [IpcClient] Received IPC response: {"RequestId":"c8118b6c-5990-40ce-be75-5cfca1e1e344","Type":5,"Timestamp":"2025-08-01T20:24:12.7972309Z","Data":"TUN stopped successfully","Error":null}
  [2025-08-01 23:54:12.798] [IpcClient] TUN stopped successfully via elevated helper
  [2025-08-01 23:54:12.810] [Connection] 🔄 Flushing DNS cache to prevent resolution issues
  [2025-08-01 23:54:12.843] [Connection] ✅ DNS cache flushed successfully
  [2025-08-01 23:54:13.844] [Connection] ✅ Comprehensive cleanup completed successfully
  [2025-08-01 23:54:13.844] [Connection] Found xray.exe in application base directory: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\bin\Debug\net8.0-windows\xray.exe
  [2025-08-01 23:54:13.844] [Connection] Found xray.exe in application base directory: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\bin\Debug\net8.0-windows\xray.exe
  [2025-08-01 23:54:13.844] [Connection] 🔄 Clearing DNS cache before TUN connection
  [2025-08-01 23:54:13.844] [Connection] 🔄 Flushing DNS cache to prevent resolution issues
  [2025-08-01 23:54:13.881] [Connection] ✅ DNS cache flushed successfully
  [2025-08-01 23:54:13.881] [Connection] Starting elevated helper process
  [2025-08-01 23:54:13.881] [Connection] Elevated helper started successfully
  [2025-08-01 23:54:13.883] [Connection] SingBox config exported to: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\debug\singbox_config.txt
  [2025-08-01 23:54:13.883] [Connection] Starting TUN connection via elevated helper
  [2025-08-01 23:54:13.883] [ElevatedTunService] Starting TUN connection for proxy: 🅼🅴🅷🅳🅸
  [2025-08-01 23:54:13.884] [Settings] 🔧 MaxConcurrentTests setter called: 10 -> 37
  [2025-08-01 23:54:13.884] [Settings] 🔧 MaxConcurrentTests clamped value: 37
  [2025-08-01 23:54:13.884] [Settings] ✅ MaxConcurrentTests property changed, triggering OnTestSettingsChanged()
  [2025-08-01 23:54:13.884] [Settings] 🔔 OnTestSettingsChanged() called
  [2025-08-01 23:54:13.884] [Settings] 📊 Current values: MaxConcurrent=37, Timeout=10000ms
  [2025-08-01 23:54:13.884] [Settings] 📡 TestSettingsChanged event has 0 subscribers
  [2025-08-01 23:54:13.884] [ElevatedTunService] TUN start attempt 1/3
  [2025-08-01 23:54:13.884] [Settings] ⚠️ TestSettingsChanged event is null - no subscribers!
  [2025-08-01 23:54:13.884] [ElevatedTunService] Debug logs export: False
  [2025-08-01 23:54:13.884] [Settings] 🔧 TestTimeoutSeconds setter called: 10s -> 10s
  [2025-08-01 23:54:13.884] [Settings] 🔧 TestTimeoutSeconds converted to milliseconds: 10000ms
  [2025-08-01 23:54:13.884] [Settings] ⏭️ TestTimeoutSeconds property unchanged
  [2025-08-01 23:54:13.884] [Settings] ⏭️ DownloadTestMethod property unchanged
  [2025-08-01 23:54:13.884] [Settings] 🔔 OnConnectionSettingsChanged() called
  [2025-08-01 23:54:13.884] [Settings] 📊 Current connection values: SelectedNetworkAdapter=auto, UseGeolocationRouting=True, PreventMitmAttacks=True, UseDnsOverHttps=True, BlockQuicProtocol=True
  [2025-08-01 23:54:13.884] [Settings] 📡 ConnectionSettingsChanged event has 0 subscribers
  [2025-08-01 23:54:13.884] [Settings] 🔧 DownloadTestMethod setter called: StaticDownload -> StaticDownload
  [2025-08-01 23:54:13.884] [Settings] ⚠️ ConnectionSettingsChanged event is null - no subscribers!
  [2025-08-01 23:54:13.884] [Settings] 🔔 OnConnectionSettingsChanged() called
  [2025-08-01 23:54:13.884] [Settings] 📊 Current connection values: SelectedNetworkAdapter=auto, UseGeolocationRouting=True, PreventMitmAttacks=True, UseDnsOverHttps=False, BlockQuicProtocol=True
  [2025-08-01 23:54:13.884] [Settings] 📡 ConnectionSettingsChanged event has 0 subscribers
  [2025-08-01 23:54:13.884] [Settings] ⚠️ ConnectionSettingsChanged event is null - no subscribers!
  [2025-08-01 23:54:13.884] [Settings] 🔔 OnConnectionSettingsChanged() called
  [2025-08-01 23:54:13.884] [Settings] 📊 Current connection values: SelectedNetworkAdapter=auto, UseGeolocationRouting=True, PreventMitmAttacks=True, UseDnsOverHttps=False, BlockQuicProtocol=True
  [2025-08-01 23:54:13.884] [Settings] ⏭️ DownloadTestSource property unchanged
  [2025-08-01 23:54:13.884] [Settings] ⚠️ ConnectionSettingsChanged event is null - no subscribers!
  [2025-08-01 23:54:13.884] [Settings] 🔧 MaxConcurrentDownloadTests setter called: 3 -> 3
  [2025-08-01 23:54:13.884] [Settings] 🔧 MaxConcurrentDownloadTests clamped value: 3
  [2025-08-01 23:54:13.884] [Settings] 📡 ConnectionSettingsChanged event has 0 subscribers
  [2025-08-01 23:54:13.884] [Settings] 🔧 DownloadTestTimeoutSeconds setter called: 30 -> 30
  [2025-08-01 23:54:13.884] [Settings] 🔧 DownloadTestTimeoutSeconds clamped value: 30
  [2025-08-01 23:54:13.884] [Settings] ⏭️ MaxConcurrentDownloadTests property unchanged
  [2025-08-01 23:54:13.884] [Settings] 🔧 DownloadTestSource setter called: http://ipv4.download.thinkbroadband.com/1MB.zip -> http://ipv4.download.thinkbroadband.com/1MB.zip
  [2025-08-01 23:54:13.884] [Settings] ⏭️ DownloadTestTimeoutSeconds property unchanged
  [2025-08-01 23:54:13.906] [IpcClient] Sent IPC message: StartTun
  [2025-08-01 23:54:14.262] [PropertyChanged] SelectedTabIndex changed
  [2025-08-01 23:54:14.262] [FullDebug-PropertyChange] SelectedTabIndex = Tab 4 (Debug)
  [2025-08-01 23:54:14.262] [Navigation] Tab switched to: Debug (index 4)
  [2025-08-01 23:54:14.263] [FullDebug-UI] Tab Switch -> Clicked (Switched to Debug tab (index 4))
  [2025-08-01 23:54:14.263] [FullDebug-Navigation] User navigated from tab 1 to tab 4
  [2025-08-01 23:54:22.058] [IpcClient] Received IPC response: {"RequestId":"5742a458-7437-4be5-b5ef-f6c78b05ac58","Type":5,"Timestamp":"2025-08-01T20:24:22.0552794Z","Data":"TUN started successfully","Error":null}
  [2025-08-01 23:54:22.058] [Connection] TUN connection started successfully via elevated helper
  [2025-08-01 23:54:22.058] [IpcClient] TUN started successfully via elevated helper
  [2025-08-01 23:54:22.058] [ElevatedTunService] TUN started successfully
  [2025-08-01 23:54:23.059] [IpcClient] Sent IPC message: StatusRequest
  [2025-08-01 23:54:23.060] [IpcClient] Received IPC response: {"Status":2,"ProcessId":11040,"StartTime":"2025-08-01T23:54:13.935188+03:30","CurrentProxy":"\uD83C\uDD7C\uD83C\uDD74\uD83C\uDD77\uD83C\uDD73\uD83C\uDD78","LocalPort":31677,"LastError":null,"RequestId":"39a59700-6e86-4c66-b5ad-e60b7b7cf75a","Type":3,"Timestamp":"2025-08-01T20:24:23.0604818Z","Data":null,"Error":null}
  [2025-08-01 23:54:23.060] [Connection] TUN process verified as Running after 1000ms
  [2025-08-01 23:54:23.060] [Connection] 🔍 Verifying TUN interface functionality
  [2025-08-01 23:54:23.176] [Connection] ✅ TUN interface found: singbox_tun (Status: Up)
  [2025-08-01 23:54:23.176] [Connection] 🔍 Testing DNS resolution through TUN interface
  [2025-08-01 23:54:23.176] [Connection] 🔍 Testing DNS resolution with 5s timeout (DoH: False, Exposed: True)
  [2025-08-01 23:54:23.290] [Connection] ✅ DNS resolution successful with 1 addresses
  [2025-08-01 23:54:23.290] [Connection] 🔍 Testing basic connectivity through proxy
  [2025-08-01 23:54:31.671] [PropertyChanged] SelectedTabIndex changed
  [2025-08-01 23:54:31.671] [FullDebug-PropertyChange] SelectedTabIndex = Tab 3 (Settings)
  [2025-08-01 23:54:31.671] [Navigation] Tab switched to: Settings (index 3)
  [2025-08-01 23:54:31.671] [FullDebug-UI] Tab Switch -> Clicked (Switched to Settings tab (index 3))
  [2025-08-01 23:54:31.671] [FullDebug-Navigation] User navigated from tab 4 to tab 3
  [2025-08-01 23:54:31.675] [Settings] 🔄 ConnectionSettingsView loaded, refreshing network adapter binding
  [2025-08-01 23:54:31.675] [Settings] 🔄 RefreshNetworkAdapterProperties called
  [2025-08-01 23:54:31.675] [Settings]    Current SelectedNetworkAdapter: 'auto'
  [2025-08-01 23:54:31.675] [PropertyChanged] Settings.AvailableNetworkAdapters changed
  [2025-08-01 23:54:31.675] [FullDebug-PropertyChange] Settings.AvailableNetworkAdapters = Value not tracked
  [2025-08-01 23:54:31.675] [Settings] ✅ Network adapter properties refresh initiated on ConnectionSettingsView load
  [2025-08-01 23:54:31.776] [PropertyChanged] Settings.SelectedNetworkAdapter changed
  [2025-08-01 23:54:31.776] [FullDebug-PropertyChange] Settings.SelectedNetworkAdapter = Value not tracked
  [2025-08-01 23:54:31.776] [Settings] ✅ RefreshNetworkAdapterProperties completed
  [2025-08-01 23:54:31.802] [PropertyChanged] Settings.AvailableNetworkAdapters changed
  [2025-08-01 23:54:31.802] [FullDebug-PropertyChange] Settings.AvailableNetworkAdapters = Value not tracked
  [2025-08-01 23:54:31.802] [PropertyChanged] Settings.SelectedNetworkAdapter changed
  [2025-08-01 23:54:31.802] [FullDebug-PropertyChange] Settings.SelectedNetworkAdapter = Value not tracked
  [2025-08-01 23:54:31.802] [Settings] 🔄 Network adapter UI binding refreshed via Dispatcher
  [2025-08-01 23:54:33.291] [Connection] Proxy connectivity test failed: The request was canceled due to the configured HttpClient.Timeout of 10 seconds elapsing.
  [2025-08-01 23:54:33.291] [Connection] ❌ Proxy connectivity test failed
  [2025-08-01 23:54:33.291] [Connection] TUN connection failed: TUN interface verification failed - interface not functional
  [2025-08-01 23:54:33.292] [IpcClient] Sent IPC message: StopTun
  [2025-08-01 23:54:39.310] [IpcClient] Received IPC response: {"RequestId":"5f82e11a-69f0-4033-9240-fb707d0c16f5","Type":5,"Timestamp":"2025-08-01T20:24:39.3098385Z","Data":"TUN stopped successfully","Error":null}
  [2025-08-01 23:54:39.310] [IpcClient] TUN stopped successfully via elevated helper
  [2025-08-01 23:54:39.312] [IpcClient] Sent IPC message: StopTun
  [2025-08-01 23:54:39.314] [IpcClient] Received IPC response: {"RequestId":"5b1e8150-1795-433f-953e-93207863ed62","Type":5,"Timestamp":"2025-08-01T20:24:39.3136104Z","Data":"TUN stopped successfully","Error":null}
  [2025-08-01 23:54:39.314] [IpcClient] TUN stopped successfully via elevated helper
  [2025-08-01 23:54:39.314] [Connection] Found xray.exe in application base directory: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\bin\Debug\net8.0-windows\xray.exe
  [2025-08-01 23:54:39.314] [Connection] Found xray.exe in application base directory: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\bin\Debug\net8.0-windows\xray.exe
  [2025-08-01 23:54:39.314] [Error] ConnectProxyTUN failed: Connection failed: TUN interface verification failed - interface not functional
  [2025-08-01 23:54:39.314] [Error] Inner exception: TUN interface verification failed - interface not functional
  [2025-08-01 23:54:39.314] [PropertyChanged] StatusMessage changed
  [2025-08-01 23:54:39.314] [FullDebug-PropertyChange] StatusMessage = 'TUN connection failed: Connection failed: TUN interface verification failed - interface not functional'
  [2025-08-01 23:54:40.416] [Settings] 🔔 OnConnectionSettingsChanged() called
  [2025-08-01 23:54:40.416] [Settings] 📊 Current connection values: SelectedNetworkAdapter=auto, UseGeolocationRouting=True, PreventMitmAttacks=True, UseDnsOverHttps=True, BlockQuicProtocol=True
  [2025-08-01 23:54:40.416] [Settings] 📡 ConnectionSettingsChanged event has 1 subscribers
  [2025-08-01 23:54:40.416] [Settings] 🚀 Invoking ConnectionSettingsChanged event...
  [2025-08-01 23:54:40.416] [Settings] ✅ ConnectionSettingsChanged event invoked successfully
  [2025-08-01 23:54:40.416] [Settings] 🔔 *** PROPERTY CHANGED: UseDnsOverHttps (POST-LOAD) ***
  [2025-08-01 23:54:40.416] [Settings] 🔔 *** PROPERTY CHANGED: UseExposedDns (POST-LOAD) ***
  [2025-08-01 23:54:40.416] [Settings] 🔔 *** CONNECTION SETTINGS CHANGED EVENT TRIGGERED (POST-LOAD) ***
  [2025-08-01 23:54:40.416] [Settings] 💾 Auto-saving settings after property change: UseDnsOverHttps
  [2025-08-01 23:54:40.416] [Settings]    SelectedNetworkAdapter: auto
  [2025-08-01 23:54:40.416] [Settings]    UseGeolocationRouting: True
  [2025-08-01 23:54:40.416] [Settings]    PreventMitmAttacks: True
  [2025-08-01 23:54:40.417] [Settings] ✅ Settings auto-saved successfully after UseDnsOverHttps change
  [2025-08-01 23:54:40.416] [Settings]    UseDnsOverHttps: True
  [2025-08-01 23:54:40.417] [Error] Inner exception: The process cannot access the file 'C:\Users\<USER>\UltimateV2ray\settings.json' because it is being used by another process.
  [2025-08-01 23:54:40.417] [Storage] ✅ Settings saved successfully - File size: 1165 bytes
  [2025-08-01 23:54:40.416] [Settings]    BlockQuicProtocol: True
  [2025-08-01 23:54:40.416] [Settings] 💾 Auto-saving settings after property change: UseExposedDns
  [2025-08-01 23:54:40.416] [Connection] Geolocation routing setting updated: True
  [2025-08-01 23:54:40.416] [Connection] Security settings updated - MITM Prevention: True, DNS over HTTPS: True, Block QUIC: True, Exposed DNS: False
  [2025-08-01 23:54:40.416] [Settings] Applying connection settings: UseGeolocationRouting=True, PreventMitmAttacks=True, UseDnsOverHttps=True, BlockQuicProtocol=True
  [2025-08-01 23:54:40.416] [Settings] 💾 Auto-saving settings after connection settings change
  [2025-08-01 23:54:40.416] [Settings] Applied connection settings to service - Geolocation: True, MITM Prevention: True, DNS over HTTPS: True, Block QUIC: True, Exposed DNS: False
  [2025-08-01 23:54:40.416] [Storage] 💾 Saving settings to: C:\Users\<USER>\UltimateV2ray\settings.json
  [2025-08-01 23:54:40.418] [Storage] 📄 Settings JSON size: 1165 characters
  [2025-08-01 23:54:40.417] [Error] Settings failed: Failed to save settings: The process cannot access the file 'C:\Users\<USER>\UltimateV2ray\settings.json' because it is being used by another process.
  [2025-08-01 23:54:40.416] [Settings] ✅ Connection settings changed - auto-saved successfully
  [2025-08-01 23:54:40.418] [Storage] 📊 Settings summary: MaxConcurrent=37, Timeout=10000ms, URL=http://cp.cloudflare.com/
  [2025-08-01 23:54:40.418] [Storage] 💾 Saving settings to: C:\Users\<USER>\UltimateV2ray\settings.json
  [2025-08-01 23:54:40.416] [Storage] 📊 Settings summary: MaxConcurrent=37, Timeout=10000ms, URL=http://cp.cloudflare.com/
  [2025-08-01 23:54:40.417] [Storage] 💾 Saving settings to: C:\Users\<USER>\UltimateV2ray\settings.json
  [2025-08-01 23:54:40.419] [Settings] ✅ Settings auto-saved successfully after connection settings change
  [2025-08-01 23:54:40.416] [Storage] 📄 Settings JSON size: 1165 characters
  [2025-08-01 23:54:40.417] [Storage] 📊 Settings summary: MaxConcurrent=37, Timeout=10000ms, URL=http://cp.cloudflare.com/
  [2025-08-01 23:54:40.419] [Storage] ✅ Settings saved successfully - File size: 1165 bytes
  [2025-08-01 23:54:40.417] [Storage] 📄 Settings JSON size: 1165 characters
  [2025-08-01 23:54:40.417] [Storage] ❌ Error saving settings: The process cannot access the file 'C:\Users\<USER>\UltimateV2ray\settings.json' because it is being used by another process.
  [2025-08-01 23:54:42.479] [Settings] 🔄 SaveConnectionSettingsAsync called - saving connection settings only
  [2025-08-01 23:54:42.479] [Settings] Connection settings: SelectedNetworkAdapter=auto, ExportSingBoxDebugLogs=False, ShowHelperWindow=False, UseGeolocationRouting=True
  [2025-08-01 23:54:42.479] [Storage] 💾 Saving settings to: C:\Users\<USER>\UltimateV2ray\settings.json
  [2025-08-01 23:54:42.479] [Storage] 📊 Settings summary: MaxConcurrent=37, Timeout=10000ms, URL=http://cp.cloudflare.com/
  [2025-08-01 23:54:42.479] [Storage] 📄 Settings JSON size: 1165 characters
  [2025-08-01 23:54:42.480] [Storage] ✅ Settings saved successfully - File size: 1165 bytes
  [2025-08-01 23:54:42.480] [PropertyChanged] StatusMessage changed
  [2025-08-01 23:54:42.480] [FullDebug-PropertyChange] StatusMessage = 'Connection settings saved successfully'
  [2025-08-01 23:54:42.480] [Action] SaveConnectionSettings - Connection settings saved to storage
  [2025-08-01 23:54:42.480] [FullDebug-Action] SaveConnectionSettings - Connection settings saved to storage
  [2025-08-01 23:54:42.480] [Settings] ✅ SaveConnectionSettingsAsync completed successfully
  [2025-08-01 23:54:43.093] [PropertyChanged] SelectedTabIndex changed
  [2025-08-01 23:54:43.093] [FullDebug-PropertyChange] SelectedTabIndex = Tab 1 (Testing)
  [2025-08-01 23:54:43.093] [Navigation] Tab switched to: Testing (index 1)
  [2025-08-01 23:54:43.093] [FullDebug-UI] Tab Switch -> Clicked (Switched to Testing tab (index 1))
  [2025-08-01 23:54:43.093] [FullDebug-Navigation] User navigated from tab 3 to tab 1
  [2025-08-01 23:54:43.985] [PropertyChanged] SelectedTabIndex changed
  [2025-08-01 23:54:43.985] [FullDebug-PropertyChange] SelectedTabIndex = Tab 0 (Fetching Settings)
  [2025-08-01 23:54:43.985] [Navigation] Tab switched to: Fetching Settings (index 0)
  [2025-08-01 23:54:43.985] [FullDebug-UI] Tab Switch -> Clicked (Switched to Fetching Settings tab (index 0))
  [2025-08-01 23:54:43.985] [FullDebug-Navigation] User navigated from tab 1 to tab 0
  [2025-08-01 23:54:44.079] [NetworkAdapter] Found 9 total network interfaces
  [2025-08-01 23:54:44.079] [NetworkAdapter]   - Ethernet 2: Ethernet, Status: Down
  [2025-08-01 23:54:44.079] [NetworkAdapter]   - Local Area Connection: 53, Status: Down
  [2025-08-01 23:54:44.079] [NetworkAdapter]   - PdaNet Broadband Connection: Ethernet, Status: Down
  [2025-08-01 23:54:44.079] [NetworkAdapter]   - Local Area Connection* 1: Wireless80211, Status: Down [WiFi]
  [2025-08-01 23:54:44.079] [NetworkAdapter]   - Local Area Connection* 2: Wireless80211, Status: Down [WiFi]
  [2025-08-01 23:54:44.079] [NetworkAdapter]   - Ethernet: Ethernet, Status: Up
  [2025-08-01 23:54:44.079] [NetworkAdapter]   - Wi-Fi: Wireless80211, Status: Down [WiFi]
  [2025-08-01 23:54:44.079] [NetworkAdapter]   - Loopback Pseudo-Interface 1: Loopback, Status: Up
  [2025-08-01 23:54:44.079] [NetworkAdapter]   - vEthernet (Default Switch): Ethernet, Status: Up
  [2025-08-01 23:54:44.079] [NetworkAdapter] Filtering out virtual WiFi adapter: Local Area Connection* 1 (Microsoft Wi-Fi Direct Virtual Adapter)
  [2025-08-01 23:54:44.079] [NetworkAdapter] Filtering out virtual WiFi adapter: Local Area Connection* 2 (Microsoft Wi-Fi Direct Virtual Adapter #2)
  [2025-08-01 23:54:44.079] [NetworkAdapter] Filtering out virtual WiFi adapter: Local Area Connection* 2 (Microsoft Wi-Fi Direct Virtual Adapter #2)
  [2025-08-01 23:54:44.079] [NetworkAdapter] After filtering: 3 suitable network interfaces (1 WiFi adapters)
  [2025-08-01 23:54:44.079] [NetworkAdapter] Filtering out virtual WiFi adapter: Local Area Connection* 1 (Microsoft Wi-Fi Direct Virtual Adapter)
  [2025-08-01 23:54:44.079] [NetworkAdapter] Filtering out virtual WiFi adapter: Local Area Connection* 1 (Microsoft Wi-Fi Direct Virtual Adapter)
  [2025-08-01 23:54:44.079] [NetworkAdapter] Added adapter: Ethernet (Ethernet) - Status: Up
  [2025-08-01 23:54:44.079] [NetworkAdapter] Filtering out virtual WiFi adapter: Local Area Connection* 2 (Microsoft Wi-Fi Direct Virtual Adapter #2)
  [2025-08-01 23:54:44.079] [NetworkAdapter] Added adapter: vEthernet (Default Switch) (Ethernet) - Status: Up
  [2025-08-01 23:54:44.079] [NetworkAdapter] Added adapter: Wi-Fi (Wi-Fi) - Status: Down
  [2025-08-01 23:54:44.079] [NetworkAdapter] Found 3 network adapters (plus Auto option)
  [2025-08-01 23:54:44.079] [CustomRules] NetworkAdapterService returned 4 adapters
  [2025-08-01 23:54:44.079] [CustomRules]   - Auto (System Default) (auto) - Auto
  [2025-08-01 23:54:44.079] [CustomRules]   - Ethernet ({6EECE4EF-61E3-434D-AE99-23853E80916F}) - Ethernet
  [2025-08-01 23:54:44.079] [CustomRules]   - vEthernet (Default Switch) ({0F4DAF6C-E4AB-40E4-A17D-C9ACC093EF53}) - Ethernet
  [2025-08-01 23:54:44.079] [CustomRules]   - Wi-Fi ({3F1854FC-3D4E-4695-A19F-02A562B3FA98}) - Wi-Fi
  [2025-08-01 23:54:44.080] [PropertyChanged] AvailableNetworkAdapters changed
  [2025-08-01 23:54:44.080] [FullDebug-PropertyChange] AvailableNetworkAdapters = Value not tracked
  [2025-08-01 23:54:44.080] [CustomRules] Refreshed custom rules with 4 network adapters
  [2025-08-01 23:54:44.080] [CustomRules] Loaded 0 rules from disk
  [2025-08-01 23:54:44.080] [CustomRules] Loaded 0 saved rules
  [2025-08-01 23:54:44.364] [PropertyChanged] SelectedTabIndex changed
  [2025-08-01 23:54:44.364] [FullDebug-PropertyChange] SelectedTabIndex = Tab 1 (Testing)
  [2025-08-01 23:54:44.364] [Navigation] Tab switched to: Testing (index 1)
  [2025-08-01 23:54:44.364] [FullDebug-UI] Tab Switch -> Clicked (Switched to Testing tab (index 1))
  [2025-08-01 23:54:44.364] [FullDebug-Navigation] User navigated from tab 0 to tab 1
  [2025-08-01 23:54:47.882] [Connection] Found sing-box.exe in application base directory: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\bin\Debug\net8.0-windows\sing-box.exe
  [2025-08-01 23:54:47.882] [PropertyChanged] StatusMessage changed
  [2025-08-01 23:54:47.882] [FullDebug-PropertyChange] StatusMessage = 'Connecting to proxy (TUN mode)...'
  [2025-08-01 23:54:47.882] [Action] ConnectProxyTUN - Attempting TUN connection to 🅼🅴🅷🅳🅸 (brainy-party.seotoolsforyou.co.uk:80)
  [2025-08-01 23:54:47.882] [FullDebug-Action] ConnectProxyTUN - Attempting TUN connection to 🅼🅴🅷🅳🅸 (brainy-party.seotoolsforyou.co.uk:80)
  [2025-08-01 23:54:47.882] [User] ConnectProxyTUN (User initiated TUN connection to 🅼🅴🅷🅳🅸)
  [2025-08-01 23:54:47.882] [FullDebug-UserInteraction] ConnectProxyTUN (User initiated TUN connection to 🅼🅴🅷🅳🅸)
  [2025-08-01 23:54:47.882] [FullDebug-Connection] Starting TUN connection process for 🅼🅴🅷🅳🅸 - Protocol: vless
  [2025-08-01 23:54:47.882] [Connection] 🧹 Starting comprehensive cleanup before new connection
  [2025-08-01 23:54:47.888] [Connection] 🛑 Stopping elevated TUN service
  [2025-08-01 23:54:47.895] [IpcClient] Sent IPC message: StopTun
  [2025-08-01 23:54:47.899] [IpcClient] Received IPC response: {"RequestId":"a59b5e6e-1474-4455-b2ba-339bffa1ccde","Type":5,"Timestamp":"2025-08-01T20:24:47.8991927Z","Data":"TUN stopped successfully","Error":null}
  [2025-08-01 23:54:47.899] [IpcClient] TUN stopped successfully via elevated helper
  [2025-08-01 23:54:47.912] [Connection] 🔄 Flushing DNS cache to prevent resolution issues
  [2025-08-01 23:54:47.947] [Connection] ✅ DNS cache flushed successfully
  [2025-08-01 23:54:48.946] [Connection] ✅ Comprehensive cleanup completed successfully
  [2025-08-01 23:54:48.946] [Connection] Found xray.exe in application base directory: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\bin\Debug\net8.0-windows\xray.exe
  [2025-08-01 23:54:48.946] [Connection] Found xray.exe in application base directory: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\bin\Debug\net8.0-windows\xray.exe
  [2025-08-01 23:54:48.947] [Connection] 🔄 Clearing DNS cache before TUN connection
  [2025-08-01 23:54:48.947] [Connection] 🔄 Flushing DNS cache to prevent resolution issues
  [2025-08-01 23:54:48.980] [Connection] ✅ DNS cache flushed successfully
  [2025-08-01 23:54:48.981] [Connection] Starting elevated helper process
  [2025-08-01 23:54:48.981] [Connection] Elevated helper started successfully
  [2025-08-01 23:54:48.983] [Connection] SingBox config exported to: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\debug\singbox_config.txt
  [2025-08-01 23:54:48.983] [Connection] Starting TUN connection via elevated helper
  [2025-08-01 23:54:48.983] [ElevatedTunService] Starting TUN connection for proxy: 🅼🅴🅷🅳🅸
  [2025-08-01 23:54:48.983] [Settings] 🔧 MaxConcurrentTests setter called: 10 -> 37
  [2025-08-01 23:54:48.983] [Settings] 🔧 MaxConcurrentTests clamped value: 37
  [2025-08-01 23:54:48.983] [Settings] ✅ MaxConcurrentTests property changed, triggering OnTestSettingsChanged()
  [2025-08-01 23:54:48.983] [Settings] 🔔 OnTestSettingsChanged() called
  [2025-08-01 23:54:48.983] [Settings] 📊 Current values: MaxConcurrent=37, Timeout=10000ms
  [2025-08-01 23:54:48.983] [Settings] 📡 TestSettingsChanged event has 0 subscribers
  [2025-08-01 23:54:48.983] [Settings] ⚠️ TestSettingsChanged event is null - no subscribers!
  [2025-08-01 23:54:48.983] [Settings] 🔧 TestTimeoutSeconds setter called: 10s -> 10s
  [2025-08-01 23:54:48.983] [Settings] 🔧 TestTimeoutSeconds converted to milliseconds: 10000ms
  [2025-08-01 23:54:48.983] [Settings] ⏭️ TestTimeoutSeconds property unchanged
  [2025-08-01 23:54:48.983] [Settings] 🔔 OnConnectionSettingsChanged() called
  [2025-08-01 23:54:48.983] [Settings] 📊 Current connection values: SelectedNetworkAdapter=auto, UseGeolocationRouting=True, PreventMitmAttacks=True, UseDnsOverHttps=True, BlockQuicProtocol=True
  [2025-08-01 23:54:48.983] [Settings] 📡 ConnectionSettingsChanged event has 0 subscribers
  [2025-08-01 23:54:48.983] [Settings] ⚠️ ConnectionSettingsChanged event is null - no subscribers!
  [2025-08-01 23:54:48.983] [Settings] 🔧 MaxConcurrentDownloadTests setter called: 3 -> 3
  [2025-08-01 23:54:48.983] [Settings] 🔧 MaxConcurrentDownloadTests clamped value: 3
  [2025-08-01 23:54:48.983] [ElevatedTunService] TUN start attempt 1/3
  [2025-08-01 23:54:48.983] [Settings] ⏭️ MaxConcurrentDownloadTests property unchanged
  [2025-08-01 23:54:48.983] [Settings] 🔧 DownloadTestTimeoutSeconds setter called: 30 -> 30
  [2025-08-01 23:54:48.983] [Settings] 🔧 DownloadTestTimeoutSeconds clamped value: 30
  [2025-08-01 23:54:48.983] [Settings] ⏭️ DownloadTestTimeoutSeconds property unchanged
  [2025-08-01 23:54:48.983] [Settings] 🔧 DownloadTestSource setter called: http://ipv4.download.thinkbroadband.com/1MB.zip -> http://ipv4.download.thinkbroadband.com/1MB.zip
  [2025-08-01 23:54:48.983] [Settings] ⏭️ DownloadTestSource property unchanged
  [2025-08-01 23:54:48.983] [Settings] 🔧 DownloadTestMethod setter called: StaticDownload -> StaticDownload
  [2025-08-01 23:54:48.983] [Settings] ⏭️ DownloadTestMethod property unchanged
  [2025-08-01 23:54:48.983] [ElevatedTunService] Debug logs export: False
  [2025-08-01 23:54:48.989] [IpcClient] Sent IPC message: StartTun
  [2025-08-01 23:54:57.086] [Connection] TUN connection started successfully via elevated helper
  [2025-08-01 23:54:57.086] [IpcClient] Received IPC response: {"RequestId":"0b2ae176-678d-427e-b1c7-c610ff059592","Type":5,"Timestamp":"2025-08-01T20:24:57.0856468Z","Data":"TUN started successfully","Error":null}
  [2025-08-01 23:54:57.086] [ElevatedTunService] TUN started successfully
  [2025-08-01 23:54:57.086] [IpcClient] TUN started successfully via elevated helper
  [2025-08-01 23:54:58.087] [IpcClient] Sent IPC message: StatusRequest
  [2025-08-01 23:54:58.088] [IpcClient] Received IPC response: {"Status":2,"ProcessId":19184,"StartTime":"2025-08-01T23:54:49.0137865+03:30","CurrentProxy":"\uD83C\uDD7C\uD83C\uDD74\uD83C\uDD77\uD83C\uDD73\uD83C\uDD78","LocalPort":32162,"LastError":null,"RequestId":"6df2407d-b4a9-41bb-9312-af4a5d0a3f5b","Type":3,"Timestamp":"2025-08-01T20:24:58.087911Z","Data":null,"Error":null}
  [2025-08-01 23:54:58.088] [Connection] TUN process verified as Running after 1000ms
  [2025-08-01 23:54:58.088] [Connection] 🔍 Verifying TUN interface functionality
  [2025-08-01 23:54:58.208] [Connection] ✅ TUN interface found: singbox_tun (Status: Up)
  [2025-08-01 23:54:58.208] [Connection] 🔍 Testing DNS resolution through TUN interface
  [2025-08-01 23:54:58.208] [Connection] 🔍 Testing DNS resolution with 15s timeout (DoH: True, Exposed: False)
  [2025-08-01 23:54:58.299] [Connection] ✅ DNS resolution successful with 1 addresses
  [2025-08-01 23:54:58.299] [Connection] 🔍 Testing basic connectivity through proxy
  [2025-08-01 23:54:58.347] [Connection] ❌ Proxy connectivity test failed
  [2025-08-01 23:54:58.348] [Connection] TUN connection failed: TUN interface verification failed - interface not functional
  [2025-08-01 23:54:58.349] [IpcClient] Sent IPC message: StopTun
  [2025-08-01 23:55:04.609] [IpcClient] Received IPC response: {"RequestId":"2e9049ba-0c72-4816-9dd1-c1699c75f3f4","Type":5,"Timestamp":"2025-08-01T20:25:04.6029901Z","Data":"TUN stopped successfully","Error":null}
  [2025-08-01 23:55:04.609] [IpcClient] TUN stopped successfully via elevated helper
  [2025-08-01 23:55:04.810] [IpcClient] Sent IPC message: StopTun
  [2025-08-01 23:55:05.082] [IpcClient] Received IPC response: {"RequestId":"c26c8280-7326-4f20-a990-1d7ba9e039cf","Type":5,"Timestamp":"2025-08-01T20:25:05.0730776Z","Data":"TUN stopped successfully","Error":null}
  [2025-08-01 23:55:05.082] [IpcClient] TUN stopped successfully via elevated helper
  [2025-08-01 23:55:05.083] [Connection] Found xray.exe in application base directory: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\bin\Debug\net8.0-windows\xray.exe
  [2025-08-01 23:55:05.083] [Connection] Found xray.exe in application base directory: E:\Prsonal Files\Projects\C#\ProxyMaster\V4 - Copy\WPF1\WpfApp1\bin\Debug\net8.0-windows\xray.exe
  [2025-08-01 23:55:05.083] [Error] ConnectProxyTUN failed: Connection failed: TUN interface verification failed - interface not functional
  [2025-08-01 23:55:05.083] [Error] Inner exception: TUN interface verification failed - interface not functional
  [2025-08-01 23:55:05.083] [PropertyChanged] StatusMessage changed
  [2025-08-01 23:55:05.083] [FullDebug-PropertyChange] StatusMessage = 'TUN connection failed: Connection failed: TUN interface verification failed - interface not functional'
  [2025-08-01 23:57:08.941] [PropertyChanged] SelectedTabIndex changed
  [2025-08-01 23:57:08.941] [FullDebug-PropertyChange] SelectedTabIndex = Tab 4 (Debug)
  [2025-08-01 23:57:08.941] [Navigation] Tab switched to: Debug (index 4)
  [2025-08-01 23:57:08.941] [FullDebug-UI] Tab Switch -> Clicked (Switched to Debug tab (index 4))
  [2025-08-01 23:57:08.941] [FullDebug-Navigation] User navigated from tab 1 to tab 4

=== END OF DEBUG LOG - 550 TOTAL MESSAGES ===
  [2025-08-01 23:57:08.941] [PropertyChanged] SelectedTabIndex changed
  [2025-08-01 23:57:08.941] [FullDebug-PropertyChange] SelectedTabIndex = Tab 4 (Debug)
  [2025-08-01 23:57:08.941] [Navigation] Tab switched to: Debug (index 4)
  [2025-08-01 23:57:08.941] [FullDebug-UI] Tab Switch -> Clicked (Switched to Debug tab (index 